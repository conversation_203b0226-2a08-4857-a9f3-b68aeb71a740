# Synthetic Patient Data Generator for Care Home Placement Prediction
import numpy as np
import pandas as pd

# For reproducibility
np.random.seed(42)

# === CONFIGURATION ===
N = 2000  # 👈 Change this number to increase/decrease the number of records

# === GENERATE SYNTHETIC DATA ===
age = np.random.randint(65, 95, N)
gender = np.random.choice(['Male', 'Female'], N, p=[0.45, 0.55])
ethnicity = np.random.choice(['White', 'Black', 'Asian', 'Hispanic', 'Other'], N, p=[0.7, 0.12, 0.05, 0.1, 0.03])
marital_status = np.random.choice(['Married', 'Single', 'Widowed', 'Divorced'], N, p=[0.4, 0.1, 0.4, 0.1])
living_arrangement = np.random.choice(['Alone', 'With Spouse', 'With Family', 'Assisted Living'], N, p=[0.3, 0.4, 0.25, 0.05])
education = np.random.choice(['None', 'High School', 'College', 'Graduate'], N, p=[0.1, 0.5, 0.3, 0.1])

diagnosis = np.random.choice(['Dementia', 'Stroke', '<PERSON>', '<PERSON><PERSON>', 'COPD', 'Diabetes', 'None'], N, p=[0.2, 0.15, 0.1, 0.15, 0.1, 0.15, 0.15])
comorbidities = np.random.poisson(2.5, N)
mmse = np.clip(np.random.normal(24, 4, N), 0, 30)  # Cognitive function
moca = np.clip(np.random.normal(22, 4, N), 0, 30)
adl = np.clip(np.random.normal(4.5, 1.5, N), 0, 6)  # Daily living
iadl = np.clip(np.random.normal(5.5, 2, N), 0, 8)
medications = np.random.poisson(6, N)
mobility = np.random.choice(['Independent', 'Walker', 'Wheelchair', 'Bedbound'], N, p=[0.6, 0.25, 0.1, 0.05])
incontinence = np.random.choice([0, 1], N, p=[0.7, 0.3])

gds = np.clip(np.random.normal(5, 2.5, N), 0, 15)  # Depression
social_support = np.random.choice(['None', 'Low', 'Moderate', 'High'], N, p=[0.1, 0.2, 0.4, 0.3])
isolation_risk = np.random.choice(['Low', 'Medium', 'High'], N, p=[0.5, 0.3, 0.2])

home_safety = np.random.choice(['Safe', 'Minor Hazards', 'Unsafe'], N, p=[0.6, 0.3, 0.1])
accessibility = np.random.choice(['None', 'Partial', 'Full'], N, p=[0.3, 0.4, 0.3])
fall_history = np.random.poisson(0.8, N)
caregiver = np.random.choice([0, 1], N, p=[0.5, 0.5])

income = np.clip(np.random.normal(35000, 15000, N), 10000, 100000)
insurance = np.random.choice(['None', 'Public', 'Private', 'Both'], N, p=[0.05, 0.6, 0.2, 0.15])
support_services = np.random.choice(['None', 'Minimal', 'Moderate', 'Extensive'], N, p=[0.3, 0.4, 0.2, 0.1])
home_care = np.random.choice([0, 1], N, p=[0.6, 0.4])

hospital_adm = np.random.poisson(1.2, N)
care_facility_stays = np.random.poisson(0.4, N)
community_services = np.random.choice([0, 1], N, p=[0.4, 0.6])

# === BUILD DATAFRAME ===
df = pd.DataFrame({
    'Age': age,
    'Gender': gender,
    'Ethnicity': ethnicity,
    'MaritalStatus': marital_status,
    'LivingArrangement': living_arrangement,
    'Education': education,
    'Diagnosis': diagnosis,
    'Comorbidities': comorbidities,
    'MMSE': mmse,
    'MoCA': moca,
    'ADL': adl,
    'IADL': iadl,
    'Medications': medications,
    'Mobility': mobility,
    'Incontinence': incontinence,
    'GDS': gds,
    'SocialSupport': social_support,
    'IsolationRisk': isolation_risk,
    'HomeSafety': home_safety,
    'Accessibility': accessibility,
    'FallHistory': fall_history,
    'Caregiver': caregiver,
    'Income': income,
    'Insurance': insurance,
    'SupportServices': support_services,
    'HomeCare': home_care,
    'HospitalAdmissions': hospital_adm,
    'CareFacilityStays': care_facility_stays,
    'CommunityServices': community_services
})

# === DERIVED SCORES ===
df['FunctionalDependency'] = (6 - df['ADL']) + (8 - df['IADL'])

df['SocialVulnerabilityIndex'] = (
    (df['SocialSupport'] == 'None') * 3 +
    (df['SocialSupport'] == 'Low') * 2 +
    (df['IsolationRisk'] == 'High') * 2 +
    (df['LivingArrangement'] == 'Alone') * 1
)

df['CareComplexityIndex'] = (
    df['Comorbidities'] * 0.5 +
    df['Medications'] * 0.2 +
    (df['Diagnosis'].isin(['Dementia', 'Stroke', 'Parkinson'])) * 2 +
    df['Incontinence'] * 1.5 +
    (df['Mobility'] == 'Wheelchair') * 1 +
    (df['Mobility'] == 'Bedbound') * 2
)

df['FallRisk'] = np.where((df['FallHistory'] > 1) | (df['Mobility'].isin(['Wheelchair', 'Bedbound'])), 'High',
                          np.where(df['FallHistory'] > 0, 'Medium', 'Low'))

# === LABEL OUTCOME RULE ===
def assign_discharge_outcome(row):
    assisted_risk = (
        (row['MMSE'] < 20) * 3 +
        (row['ADL'] < 3) * 2 +
        (row['Mobility'] == 'Bedbound') * 3 +
        (row['Mobility'] == 'Wheelchair') * 2 +
        (row['Incontinence'] == 1) * 1 +
        (row['FallRisk'] == 'High') * 2 +
        (row['SocialVulnerabilityIndex'] >= 4) * 2 +
        (row['CareComplexityIndex'] >= 6) * 2 +
        (row['Age'] >= 85) * 1
    )

    independent_score = (
        (row['MMSE'] >= 24) * 3 +
        (row['ADL'] >= 5) * 3 +
        (row['IADL'] >= 6) * 2 +
        (row['Mobility'] == 'Independent') * 3 +
        (row['Incontinence'] == 0) * 2 +
        (row['FallRisk'] == 'Low') * 2 +
        (row['CareComplexityIndex'] <= 3) * 2 +
        (row['Age'] < 80) * 1 +
        (row['SocialSupport'] == 'High') * 1
    )

    if assisted_risk >= 8:
        return 'Assisted'
    elif independent_score >= 15:
        return 'Independent'
    else:
        return 'Home'

df['DischargeOutcome'] = df.apply(assign_discharge_outcome, axis=1)

# === EXPORT TO CSV ===
csv_filename = f'synthetic_carehome_data_{N}.csv'
df.to_csv(csv_filename, index=False)
print(f"✅ {N} records saved to: {csv_filename}")


import matplotlib.pyplot as plt
import seaborn as sns

# Select only numeric columns for correlation analysis
numeric_cols = df.select_dtypes(include=['number'])

# Compute correlation matrix
corr_matrix = numeric_cols.corr()

# Plot the heatmap
plt.figure(figsize=(16, 12))
sns.heatmap(corr_matrix, annot=True, fmt=".2f", cmap='coolwarm', square=True, linewidths=0.5)
plt.title("Correlation Matrix of Numerical Features", fontsize=16)
plt.xticks(rotation=45, ha='right')
plt.yticks(rotation=0)
plt.tight_layout()
plt.show()


import pandas as pd
import seaborn as sns
import matplotlib.pyplot as plt

# Load dataset
df = pd.read_csv("synthetic_carehome_data_2000.csv")
print("✅ Data loaded:", df.shape)

# Preview
df.head(100)


# Basic Info
df.info()

# Check for missing values
print("\nMissing values:\n", df.isnull().sum())

# Class distribution
print("\nDischarge Outcome Distribution:\n", df['DischargeOutcome'].value_counts(normalize=True))
sns.countplot(data=df, x='DischargeOutcome')
plt.title('Discharge Outcome Distribution')
plt.show()


from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.model_selection import train_test_split

# Define features
numerical_cols = [
    'Age', 'Comorbidities', 'MMSE', 'MoCA', 'ADL', 'IADL', 'Medications',
    'GDS', 'FallHistory', 'Caregiver', 'Income', 'HomeCare',
    'HospitalAdmissions', 'CareFacilityStays', 'CommunityServices',
    'FunctionalDependency', 'SocialVulnerabilityIndex', 'CareComplexityIndex'
]

categorical_cols = [
    'Gender', 'Diagnosis', 'Mobility', 'Incontinence', 'SocialSupport',
    'IsolationRisk', 'HomeSafety', 'Accessibility', 'Insurance',
    'SupportServices', 'FallRisk'
]

# Encode categorical columns
le_dict = {}
X_cat = pd.DataFrame()
for col in categorical_cols:
    le = LabelEncoder()
    X_cat[col] = le.fit_transform(df[col])
    le_dict[col] = le

# Combine numerical and categorical
X = pd.concat([df[numerical_cols], X_cat], axis=1)
y = df['DischargeOutcome']

# Encode target
y_le = LabelEncoder()
y_encoded = y_le.fit_transform(y)

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(
    X, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

# Scale numeric features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)


from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report
from sklearn.model_selection import cross_val_score

models = {
    "Random Forest": RandomForestClassifier(random_state=42),
    "Gradient Boosting": GradientBoostingClassifier(random_state=42),
    "Logistic Regression": LogisticRegression(max_iter=1000),
    "SVM": SVC(probability=True)
}

results = {}

for name, model in models.items():
    print(f"Training {name}...")
    model.fit(X_train_scaled, y_train)
    y_pred = model.predict(X_test_scaled)
    acc = accuracy_score(y_test, y_pred)
    cv = cross_val_score(model, X_train_scaled, y_train, cv=5)
    results[name] = {
        'model': model,
        'accuracy': acc,
        'cv_mean': cv.mean(),
        'cv_std': cv.std(),
        'predictions': y_pred
    }
    print(f"  ✅ Accuracy: {acc:.4f}")
    print(f"  ✅ CV Mean: {cv.mean():.4f} (+/- {cv.std() * 2:.4f})")
    print()


# Identify best model
best_model_name = max(results, key=lambda k: results[k]['accuracy'])
best_model = results[best_model_name]['model']
y_pred_best = results[best_model_name]['predictions']

print(f"✅ Best Model: {best_model_name}")
print(classification_report(y_test, y_pred_best, target_names=y_le.classes_))


# Feature importance (tree-based)
if hasattr(best_model, 'feature_importances_'):
    feat_importance = pd.DataFrame({
        'Feature': X.columns,
        'Importance': best_model.feature_importances_
    }).sort_values(by='Importance', ascending=False)

    print("\nTop 10 Important Features:")
    display(feat_importance.head(10))

    sns.barplot(data=feat_importance.head(10), x='Importance', y='Feature')
    plt.title(f"{best_model_name} - Top 10 Features")
    plt.tight_layout()
    plt.show()


from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.metrics import adjusted_rand_score, silhouette_score

# Reduce dimensionality
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X_train_scaled)

# Clustering
kmeans = KMeans(n_clusters=3, random_state=42)
cluster_labels = kmeans.fit_predict(X_train_scaled)

# Metrics
ari = adjusted_rand_score(y_train, cluster_labels)
sil_score = silhouette_score(X_train_scaled, cluster_labels)

print(f"🎯 Adjusted Rand Index: {ari:.4f}")
print(f"📐 Silhouette Score: {sil_score:.4f}")


# Visualize clusters
plt.figure(figsize=(8, 6))
sns.scatterplot(x=X_pca[:, 0], y=X_pca[:, 1], hue=cluster_labels, palette='Set2')
plt.title("KMeans Clustering (PCA projection)")
plt.xlabel("PC 1")
plt.ylabel("PC 2")
plt.show()


print("=== SUMMARY ===")
print(f"🔍 Best performing model: {best_model_name}")
print(f"✅ Accuracy: {results[best_model_name]['accuracy']:.3f}")
print(f"📊 Cross-Validation Score: {results[best_model_name]['cv_mean']:.3f}")

print("\n📌 Key Insights:")
print("- Cognitive function (MMSE) is a strong predictor")
print("- Functional & social dependency affects outcome")
print("- Complexity scores help predict care needs")
print("- Unsupervised clusters hint at latent patterns")
