import streamlit as st
import pandas as pd
import numpy as np
import joblib
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Page configuration
st.set_page_config(
    page_title="Care Placement Prediction System",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .prediction-box {
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        text-align: center;
        font-size: 1.2rem;
        font-weight: bold;
    }
    .independent { background-color: #e6f3ff; color: #4472C4; }
    .home-care { background-color: #e6f7e6; color: #70AD47; }
    .assisted { background-color: #fff0e6; color: #FF8C00; }
    .memory { background-color: #f0e6ff; color: #9966CC; }
</style>
""", unsafe_allow_html=True)

@st.cache_resource
def load_models():
    """Load all saved models and components"""
    try:
        model = joblib.load('models/best_model.pkl')
        scaler = joblib.load('models/scaler.pkl')
        label_encoder = joblib.load('models/label_encoder.pkl')
        feature_names = joblib.load('models/feature_names.pkl')
        feature_importance = joblib.load('models/feature_importance.pkl')
        return model, scaler, label_encoder, feature_names, feature_importance
    except FileNotFoundError:
        st.error("Model files not found! Please run the training notebook first.")
        return None, None, None, None, None

def create_input_form():
    """Create input form for patient data"""
    st.sidebar.header("🏥 Patient Information")
    
    # Basic Demographics
    st.sidebar.subheader("📋 Demographics")
    age = st.sidebar.slider("Age", 65, 100, 75)
    gender = st.sidebar.selectbox("Gender", ["Male", "Female"])
    
    # Cognitive Assessment
    st.sidebar.subheader("🧠 Cognitive Assessment")
    mmse = st.sidebar.slider("MMSE Score", 0, 30, 24, help="Mini-Mental State Examination (0-30)")
    moca = st.sidebar.slider("MoCA Score", 0, 30, 26, help="Montreal Cognitive Assessment (0-30)")
    
    # Functional Assessment
    st.sidebar.subheader("🏃 Functional Assessment")
    adl = st.sidebar.slider("ADL Score", 0, 6, 6, help="Activities of Daily Living (0-6)")
    iadl = st.sidebar.slider("IADL Score", 0, 8, 8, help="Instrumental Activities of Daily Living (0-8)")
    
    # Physical Health
    st.sidebar.subheader("💪 Physical Health")
    mobility = st.sidebar.selectbox("Mobility", ["Independent", "Walker", "Wheelchair", "Bedbound"])
    fall_risk = st.sidebar.selectbox("Fall Risk", ["Low", "Medium", "High"])
    
    # Mental Health
    st.sidebar.subheader("🧘 Mental Health")
    gds = st.sidebar.slider("GDS Score", 0, 15, 5, help="Geriatric Depression Scale (0-15)")
    
    # Social Support
    st.sidebar.subheader("👥 Social Support")
    social_support = st.sidebar.selectbox("Social Support Level", ["Low", "Medium", "High"])
    living_situation = st.sidebar.selectbox("Living Situation", ["Alone", "With_Family", "With_Spouse"])
    
    # Medical Information
    st.sidebar.subheader("⚕️ Medical Information")
    primary_diagnosis = st.sidebar.selectbox("Primary Diagnosis", 
        ["Dementia", "Stroke", "Heart_Disease", "Diabetes", "COPD", "Arthritis", "Other"])
    comorbidities = st.sidebar.slider("Number of Comorbidities", 0, 10, 2)
    
    return {
        'Age': age,
        'Gender': gender,
        'MMSE': mmse,
        'MoCA': moca,
        'ADL': adl,
        'IADL': iadl,
        'Mobility': mobility,
        'FallRisk': fall_risk,
        'GDS': gds,
        'SocialSupport': social_support,
        'LivingSituation': living_situation,
        'PrimaryDiagnosis': primary_diagnosis,
        'Comorbidities': comorbidities
    }

def calculate_derived_features(patient_data):
    """Calculate derived features like in the training data"""
    # Functional Dependency Score
    functional_dependency = (6 - patient_data['ADL']) + (8 - patient_data['IADL'])
    
    # Care Complexity Index (simplified version)
    complexity_score = 0
    if patient_data['MMSE'] < 20: complexity_score += 2
    if patient_data['ADL'] < 4: complexity_score += 2
    if patient_data['FallRisk'] == 'High': complexity_score += 2
    if patient_data['Comorbidities'] > 3: complexity_score += 2
    if patient_data['SocialSupport'] == 'Low': complexity_score += 1
    
    patient_data['FunctionalDependency'] = functional_dependency
    patient_data['CareComplexityIndex'] = complexity_score
    
    return patient_data

def preprocess_input(patient_data, feature_names):
    """Convert patient data to model input format"""
    # Create a dataframe with all required features
    input_df = pd.DataFrame([patient_data])
    
    # Add derived features
    input_df = pd.DataFrame([calculate_derived_features(patient_data)])
    
    # Create dummy variables for categorical features
    categorical_features = ['Gender', 'Mobility', 'FallRisk', 'SocialSupport', 
                          'LivingSituation', 'PrimaryDiagnosis']
    
    for feature in categorical_features:
        if feature in input_df.columns:
            dummies = pd.get_dummies(input_df[feature], prefix=feature)
            input_df = pd.concat([input_df, dummies], axis=1)
            input_df.drop(feature, axis=1, inplace=True)
    
    # Ensure all required features are present
    for feature in feature_names:
        if feature not in input_df.columns:
            input_df[feature] = 0
    
    # Select only the features used in training
    input_df = input_df[feature_names]
    
    return input_df

def display_prediction(prediction, probabilities, label_encoder):
    """Display prediction results with styling"""
    predicted_class = label_encoder.inverse_transform([prediction])[0]
    
    # Color mapping for predictions
    color_map = {
        'Independent_Living': 'independent',
        'Home_Care': 'home-care', 
        'Assisted_Living': 'assisted',
        'Memory_Care': 'memory'
    }
    
    # Display main prediction
    st.markdown(f"""
    <div class="prediction-box {color_map.get(predicted_class, 'independent')}">
        🎯 Recommended Care Placement: {predicted_class.replace('_', ' ')}
    </div>
    """, unsafe_allow_html=True)
    
    # Display confidence scores
    st.subheader("📊 Prediction Confidence")
    
    prob_df = pd.DataFrame({
        'Care Type': [cls.replace('_', ' ') for cls in label_encoder.classes_],
        'Probability': probabilities[0] * 100
    })
    
    # Create probability chart
    fig = px.bar(prob_df, x='Care Type', y='Probability', 
                title="Prediction Probabilities",
                color='Care Type',
                color_discrete_map={
                    'Independent Living': '#4472C4',
                    'Home Care': '#70AD47', 
                    'Assisted Living': '#FF8C00',
                    'Memory Care': '#9966CC'
                })
    fig.update_layout(showlegend=False)
    st.plotly_chart(fig, use_container_width=True)
    
    return predicted_class

def display_feature_importance(feature_importance, patient_data):
    """Display feature importance and patient values"""
    st.subheader("🔍 Key Factors in This Prediction")
    
    # Get top 10 most important features
    top_features = feature_importance.head(10)
    
    fig = px.bar(top_features, x='Importance', y='Feature', 
                orientation='h',
                title="Top 10 Most Important Features",
                color='Importance',
                color_continuous_scale='viridis')
    fig.update_layout(height=400)
    st.plotly_chart(fig, use_container_width=True)

def main():
    """Main application"""
    st.markdown('<h1 class="main-header">🏥 Care Placement Prediction System</h1>', 
                unsafe_allow_html=True)
    
    # Load models
    model, scaler, label_encoder, feature_names, feature_importance = load_models()
    
    if model is None:
        st.stop()
    
    # Create two columns
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.header("📝 Patient Assessment")
        patient_data = create_input_form()
        
        if st.button("🔮 Predict Care Placement", type="primary"):
            # Preprocess input
            input_df = preprocess_input(patient_data, feature_names)
            
            # Scale features
            input_scaled = scaler.transform(input_df)
            
            # Make prediction
            prediction = model.predict(input_scaled)
            probabilities = model.predict_proba(input_scaled)
            
            # Store results in session state
            st.session_state.prediction = prediction[0]
            st.session_state.probabilities = probabilities
            st.session_state.patient_data = patient_data
    
    with col2:
        st.header("📊 Prediction Results")
        
        if hasattr(st.session_state, 'prediction'):
            predicted_class = display_prediction(
                st.session_state.prediction, 
                st.session_state.probabilities, 
                label_encoder
            )
            
            # Display feature importance
            display_feature_importance(feature_importance, st.session_state.patient_data)
            
            # Clinical interpretation
            st.subheader("🩺 Clinical Interpretation")
            interpretations = {
                'Independent_Living': "Patient shows good cognitive and functional capacity for independent living with minimal support.",
                'Home_Care': "Patient needs professional care services but can remain in community setting with support.",
                'Assisted_Living': "Patient requires 24/7 supervision and assistance with daily activities in a facility setting.",
                'Memory_Care': "Patient needs specialized dementia care with secured environment and behavioral management."
            }
            
            st.info(interpretations.get(predicted_class, "Assessment complete."))
        
        else:
            st.info("👈 Please fill out the patient assessment form and click 'Predict Care Placement' to see results.")
    
    # Footer
    st.markdown("---")
    st.markdown("**Note:** This tool is for educational purposes only and should not replace professional clinical judgment.")

if __name__ == "__main__":
    main()
