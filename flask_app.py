from flask import Flask, render_template, request, jsonify
import pandas as pd
import numpy as np
import joblib
import os

app = Flask(__name__)

# Load models at startup
def load_models():
    """Load all saved models and components"""
    try:
        model = joblib.load('models/best_model.pkl')
        scaler = joblib.load('models/scaler.pkl')
        label_encoder = joblib.load('models/label_encoder.pkl')
        feature_names = joblib.load('models/feature_names.pkl')
        feature_importance = joblib.load('models/feature_importance.pkl')
        return model, scaler, label_encoder, feature_names, feature_importance
    except FileNotFoundError:
        print("Model files not found! Please run the training notebook first.")
        return None, None, None, None, None

# Load models globally
model, scaler, label_encoder, feature_names, feature_importance = load_models()

def calculate_derived_features(patient_data):
    """Calculate derived features like in the training data"""
    # Functional Dependency Score
    functional_dependency = (6 - patient_data['ADL']) + (8 - patient_data['IADL'])
    
    # Care Complexity Index (simplified version)
    complexity_score = 0
    if patient_data['MMSE'] < 20: complexity_score += 2
    if patient_data['ADL'] < 4: complexity_score += 2
    if patient_data['FallRisk'] == 'High': complexity_score += 2
    if patient_data['Comorbidities'] > 3: complexity_score += 2
    if patient_data['SocialSupport'] == 'Low': complexity_score += 1
    
    patient_data['FunctionalDependency'] = functional_dependency
    patient_data['CareComplexityIndex'] = complexity_score
    
    return patient_data

def preprocess_input(patient_data, feature_names):
    """Convert patient data to model input format"""
    # Create a dataframe with all required features
    input_df = pd.DataFrame([patient_data])
    
    # Add derived features
    input_df = pd.DataFrame([calculate_derived_features(patient_data)])
    
    # Create dummy variables for categorical features
    categorical_features = ['Gender', 'Mobility', 'FallRisk', 'SocialSupport', 
                          'LivingSituation', 'PrimaryDiagnosis']
    
    for feature in categorical_features:
        if feature in input_df.columns:
            dummies = pd.get_dummies(input_df[feature], prefix=feature)
            input_df = pd.concat([input_df, dummies], axis=1)
            input_df.drop(feature, axis=1, inplace=True)
    
    # Ensure all required features are present
    for feature in feature_names:
        if feature not in input_df.columns:
            input_df[feature] = 0
    
    # Select only the features used in training
    input_df = input_df[feature_names]
    
    return input_df

@app.route('/')
def home():
    """Main page with input form"""
    return render_template('index.html')

@app.route('/predict', methods=['POST'])
def predict():
    """Make prediction based on form input"""
    if model is None:
        return jsonify({'error': 'Model not loaded properly'})
    
    try:
        # Get form data
        patient_data = {
            'Age': int(request.form['age']),
            'Gender': request.form['gender'],
            'MMSE': int(request.form['mmse']),
            'MoCA': int(request.form['moca']),
            'ADL': int(request.form['adl']),
            'IADL': int(request.form['iadl']),
            'Mobility': request.form['mobility'],
            'FallRisk': request.form['fall_risk'],
            'GDS': int(request.form['gds']),
            'SocialSupport': request.form['social_support'],
            'LivingSituation': request.form['living_situation'],
            'PrimaryDiagnosis': request.form['primary_diagnosis'],
            'Comorbidities': int(request.form['comorbidities'])
        }
        
        # Preprocess input
        input_df = preprocess_input(patient_data, feature_names)
        
        # Scale features
        input_scaled = scaler.transform(input_df)
        
        # Make prediction
        prediction = model.predict(input_scaled)
        probabilities = model.predict_proba(input_scaled)
        
        # Get prediction details
        predicted_class = label_encoder.inverse_transform([prediction[0]])[0]
        
        # Create response
        response = {
            'prediction': predicted_class.replace('_', ' '),
            'probabilities': {
                cls.replace('_', ' '): float(prob) 
                for cls, prob in zip(label_encoder.classes_, probabilities[0])
            },
            'confidence': float(max(probabilities[0])) * 100
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/predict', methods=['POST'])
def api_predict():
    """API endpoint for programmatic access"""
    if model is None:
        return jsonify({'error': 'Model not loaded properly'})
    
    try:
        # Get JSON data
        patient_data = request.get_json()
        
        # Preprocess input
        input_df = preprocess_input(patient_data, feature_names)
        
        # Scale features
        input_scaled = scaler.transform(input_df)
        
        # Make prediction
        prediction = model.predict(input_scaled)
        probabilities = model.predict_proba(input_scaled)
        
        # Get prediction details
        predicted_class = label_encoder.inverse_transform([prediction[0]])[0]
        
        # Create response
        response = {
            'prediction': predicted_class,
            'probabilities': {
                cls: float(prob) 
                for cls, prob in zip(label_encoder.classes_, probabilities[0])
            },
            'confidence': float(max(probabilities[0]))
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    if model is None:
        print("Cannot start app - models not loaded!")
    else:
        print("Starting Care Placement Prediction API...")
        print("Access the web interface at: http://localhost:5000")
        print("API endpoint available at: http://localhost:5000/api/predict")
        app.run(debug=True, host='0.0.0.0', port=5000)
