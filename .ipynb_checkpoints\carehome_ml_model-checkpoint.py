# Jupyter Notebook: Care Home Placement Prediction Using Supervised and Unsupervised Learning

# ---
# 📘 Objective:
# Simulate a dataset for care placement outcomes (e.g., stay at home, assisted living, independent elderly living)
# Apply supervised learning (classification) and unsupervised learning (clustering)
# Explain features, logic, and model choices

# ---
# 📦 Step 1: Imports
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt
import seaborn as sns
np.random.seed(42)

# ---
# 🧠 Step 2: Generate Synthetic Dataset
# Features selected are based on domain knowledge of elder care and placement decisions
# 
# Features:
# - age: Older individuals are more likely to require support
# - adl_score: ADL = Activities of Daily Living (0 = dependent, 100 = independent)
# - mobility_score: Higher = better mobility
# - has_carer: Presence of a caregiver at home (1 = yes, 0 = no)
# - cognitive_decline: 1 = Yes, 0 = No
# - recent_hospital_visits: count of hospital visits in last 6 months
#
# Labels (Supervised):
# - placement: 'Home', 'Assisted Living', 'Independent Living'

n_samples = 500

data = pd.DataFrame({
    'age': np.random.normal(80, 7, n_samples).astype(int),
    'adl_score': np.random.normal(60, 20, n_samples),
    'mobility_score': np.random.normal(50, 15, n_samples),
    'has_carer': np.random.binomial(1, 0.6, n_samples),
    'cognitive_decline': np.random.binomial(1, 0.3, n_samples),
    'recent_hospital_visits': np.random.poisson(1, n_samples)
})

# Clip ADL and mobility scores to realistic bounds
data['adl_score'] = data['adl_score'].clip(0, 100)
data['mobility_score'] = data['mobility_score'].clip(0, 100)

# ---
# 🧮 Step 3: Define Labeling Logic (Simulated Ground Truth)
# This uses rules of thumb to assign likely placement categories

def classify_placement(row):
    if row['adl_score'] < 40 or row['cognitive_decline'] == 1:
        if row['has_carer'] == 0:
            return 'Assisted Living'
        else:
            return 'Home'
    elif row['adl_score'] > 70 and row['mobility_score'] > 60 and row['has_carer'] == 1:
        return 'Home'
    else:
        return 'Independent Living'

data['placement'] = data.apply(classify_placement, axis=1)

# ---
# 📊 Step 4: Supervised Learning
# Goal: Predict placement from features using Random Forest

X = data.drop('placement', axis=1)
y = data['placement']

X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, stratify=y, random_state=42)

clf = RandomForestClassifier(n_estimators=100, random_state=42)
clf.fit(X_train, y_train)
y_pred = clf.predict(X_test)

print("\n--- Classification Report (Random Forest) ---")
print(classification_report(y_test, y_pred))

# ---
# 📈 Step 5: Unsupervised Learning (KMeans Clustering)
# Goal: Group similar individuals without knowing their placement

scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)

# Reduce dimensions for visualization
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X_scaled)

kmeans = KMeans(n_clusters=3, random_state=42)
kmeans_labels = kmeans.fit_predict(X_scaled)

# Visualize clusters
plt.figure(figsize=(8, 5))
sns.scatterplot(x=X_pca[:, 0], y=X_pca[:, 1], hue=kmeans_labels, palette='Set2')
plt.title("Unsupervised Clustering (KMeans)")
plt.xlabel("PCA Component 1")
plt.ylabel("PCA Component 2")
plt.legend(title="Cluster")
plt.show()

# ---
# ✅ Summary:
# - We simulated realistic features for elderly individuals
# - Labeled data was created using domain-based rules (ADL, carer, cognition)
# - A Random Forest model predicted placements well
# - KMeans showed that some natural clusters exist based on feature similarity

# Next steps:
# - Replace synthetic data with real assessments when available
# - Tune models and validate clustering with real placement outcomes
