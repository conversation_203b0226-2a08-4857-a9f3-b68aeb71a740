{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "Patient Discharge Outcome Prediction Model\n", "\n", "This script demonstrates how to build a machine learning model to predict \n", "patient discharge outcomes: Assisted Living, Rema<PERSON> at Home, or Independent.\n", "\n", "Features include demographics, health data, psychosocial factors, home environment,\n", "financial support, and service use history.\n", "\"\"\"\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.preprocessing import StandardScaler, LabelEncoder\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.svm import SVC\n", "from sklearn.cluster import KMeans, DBSCAN\n", "from sklearn.decomposition import PCA\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, adjusted_rand_score, silhouette_score\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "\n", "def generate_synthetic_data(N=2000):\n", "    \"\"\"Generate synthetic patient data based on realistic clinical patterns.\"\"\"\n", "    print(f\"Generating {N} synthetic patient records...\")\n", "    \n", "    # Demographics\n", "    age = np.random.randint(65, 95, N)\n", "    gender = np.random.choice(['Male', 'Female'], N, p=[0.45, 0.55])\n", "    ethnicity = np.random.choice(['White', 'Black', 'Asian', 'Hispanic', 'Other'], N, p=[0.7, 0.12, 0.05, 0.1, 0.03])\n", "    marital_status = np.random.choice(['Married', 'Single', 'Widowed', 'Divorced'], N, p=[0.4, 0.1, 0.4, 0.1])\n", "    living_arrangement = np.random.choice(['Alone', 'With Spouse', 'With Family', 'Assisted Living'], N, p=[0.3, 0.4, 0.25, 0.05])\n", "    education = np.random.choice(['None', 'High School', 'College', 'Graduate'], N, p=[0.1, 0.5, 0.3, 0.1])\n", "    \n", "    # Health and Clinical Data\n", "    diagnosis = np.random.choice(['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'COPD', 'Diabetes', 'None'], N, p=[0.2, 0.15, 0.1, 0.15, 0.1, 0.15, 0.15])\n", "    comorbidities = np.random.poisson(2.5, N)\n", "    mmse = np.clip(np.random.normal(24, 4, N), 0, 30)  # Mini-Mental State Examination\n", "    moca = np.clip(np.random.normal(22, 4, N), 0, 30)  # Montreal Cognitive Assessment\n", "    adl = np.clip(np.random.normal(4.5, 1.5, N), 0, 6)  # Activities of Daily Living\n", "    iadl = np.clip(np.random.normal(5.5, 2, N), 0, 8)   # Instrumental ADL\n", "    medications = np.random.poisson(6, N)\n", "    mobility = np.random.choice(['<PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Bedbound'], N, p=[0.6, 0.25, 0.1, 0.05])\n", "    incontinence = np.random.choice([0, 1], N, p=[0.7, 0.3])\n", "    \n", "    # Psychosocial Factors\n", "    gds = np.clip(np.random.normal(5, 2.5, N), 0, 15)  # Geriatric Depression Scale\n", "    social_support = np.random.choice(['None', 'Low', 'Moderate', 'High'], N, p=[0.1, 0.2, 0.4, 0.3])\n", "    isolation_risk = np.random.choice(['Low', 'Medium', 'High'], N, p=[0.5, 0.3, 0.2])\n", "    \n", "    # Home Environment\n", "    home_safety = np.random.choice(['Safe', 'Minor Hazards', 'Unsafe'], N, p=[0.6, 0.3, 0.1])\n", "    accessibility = np.random.choice(['None', 'Partial', 'Full'], N, p=[0.3, 0.4, 0.3])\n", "    fall_history = np.random.poisson(0.8, N)\n", "    caregiver = np.random.choice([0, 1], N, p=[0.5, 0.5])\n", "    \n", "    # Financial and Support\n", "    income = np.clip(np.random.normal(35000, 15000, N), 10000, 100000)\n", "    insurance = np.random.choice(['None', 'Public', 'Private', 'Both'], N, p=[0.05, 0.6, 0.2, 0.15])\n", "    support_services = np.random.choice(['None', 'Minimal', 'Moderate', 'Extensive'], N, p=[0.3, 0.4, 0.2, 0.1])\n", "    home_care = np.random.choice([0, 1], N, p=[0.6, 0.4])\n", "    \n", "    # Service Use History\n", "    hospital_adm = np.random.poisson(1.2, N)\n", "    care_facility_stays = np.random.poisson(0.4, N)\n", "    community_services = np.random.choice([0, 1], N, p=[0.4, 0.6])\n", "    \n", "    # Create DataFrame\n", "    df = pd.DataFrame({\n", "        'Age': age,\n", "        'Gender': gender,\n", "        'Ethnicity': ethnicity,\n", "        'MaritalStatus': marital_status,\n", "        'LivingArrangement': living_arrangement,\n", "        'Education': education,\n", "        'Diagnosis': diagnosis,\n", "        'Comorbidities': comorbidities,\n", "        'MMSE': mmse,\n", "        'MoCA': moca,\n", "        'ADL': adl,\n", "        'IADL': i<PERSON><PERSON>,\n", "        'Medications': medications,\n", "        'Mobility': mobility,\n", "        'Incontinence': incontinence,\n", "        'GDS': gds,\n", "        'SocialSupport': social_support,\n", "        'IsolationRisk': isolation_risk,\n", "        'HomeSafety': home_safety,\n", "        'Accessibility': accessibility,\n", "        'FallHistory': fall_history,\n", "        'Caregiver': caregiver,\n", "        'Income': income,\n", "        'Insurance': insurance,\n", "        'SupportServices': support_services,\n", "        'HomeCare': home_care,\n", "        'HospitalAdmissions': hospital_adm,\n", "        'CareFacilityStays': care_facility_stays,\n", "        'CommunityServices': community_services\n", "    })\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def create_derived_features(df):\n", "    \"\"\"Create derived features and risk scores.\"\"\"\n", "    print(\"Creating derived features and risk scores...\")\n", "    \n", "    # Cognitive Risk Score\n", "    df['CognitiveRisk'] = np.where(df['MMSE'] < 20, 'High', \n", "                                   np.where(df['MMSE'] < 24, 'Medium', 'Low'))\n", "    \n", "    # Functional Dependency Score\n", "    df['FunctionalDependency'] = (6 - df['ADL']) + (8 - df['IADL'])\n", "    \n", "    # Fall Risk Score\n", "    df['FallRisk'] = np.where((df['FallHistory'] > 1) | (df['Mobility'].isin(['Wheelchair', 'Bedbound'])), 'High',\n", "                             np.where(df['FallHistory'] > 0, 'Medium', 'Low'))\n", "    \n", "    # Social Vulnerability Index\n", "    social_vulnerability = 0\n", "    social_vulnerability += (df['SocialSupport'] == 'None').astype(int) * 3\n", "    social_vulnerability += (df['SocialSupport'] == 'Low').astype(int) * 2\n", "    social_vulnerability += (df['IsolationRisk'] == 'High').astype(int) * 2\n", "    social_vulnerability += (df['LivingArrangement'] == 'Alone').astype(int) * 1\n", "    df['SocialVulnerabilityIndex'] = social_vulnerability\n", "    \n", "    # Care Complexity Index\n", "    care_complexity = 0\n", "    care_complexity += df['Comorbidities'] * 0.5\n", "    care_complexity += df['Medications'] * 0.2\n", "    care_complexity += (df['Diagnosis'].isin(['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Parkinson'])).astype(int) * 2\n", "    care_complexity += df['Incontinence'] * 1.5\n", "    care_complexity += (df['Mobility'] == 'Wheelchair').astype(int) * 1\n", "    care_complexity += (df['Mobility'] == 'Bedbound').astype(int) * 2\n", "    df['CareComplexityIndex'] = care_complexity\n", "    \n", "    # Age Group\n", "    df['AgeGroup'] = pd.cut(df['Age'], bins=[64, 75, 85, 95], labels=['65-74', '75-84', '85+'])\n", "    \n", "    return df\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}