#!/usr/bin/env python3
"""
Quick Start Script for Care Placement Prediction System
This script helps you get the web application running quickly.
"""

import os
import sys
import subprocess
import importlib.util

def check_package(package_name):
    """Check if a package is installed"""
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install packages. Please install manually:")
        print("   pip install -r requirements.txt")
        return False

def check_model_files():
    """Check if model files exist"""
    required_files = [
        'models/best_model.pkl',
        'models/scaler.pkl',
        'models/label_encoder.pkl',
        'models/feature_names.pkl',
        'models/feature_importance.pkl'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing model files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        print("\n📝 Please run the training notebook (care_improved.ipynb) first to generate these files.")
        return False
    
    print("✅ All model files found!")
    return True

def run_streamlit():
    """Run the Streamlit application"""
    print("🚀 Starting Streamlit application...")
    print("   The app will open in your browser at: http://localhost:8501")
    print("   Press Ctrl+C to stop the application")
    
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "care_prediction_app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped.")
    except FileNotFoundError:
        print("❌ Streamlit not found. Installing...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
        subprocess.run([sys.executable, "-m", "streamlit", "run", "care_prediction_app.py"])

def run_flask():
    """Run the Flask application"""
    print("🚀 Starting Flask application...")
    print("   The app will be available at: http://localhost:5000")
    print("   Press Ctrl+C to stop the application")
    
    try:
        subprocess.run([sys.executable, "flask_app.py"])
    except KeyboardInterrupt:
        print("\n👋 Application stopped.")

def main():
    """Main function"""
    print("🏥 Care Placement Prediction System - Quick Start")
    print("=" * 50)
    
    # Check if model files exist
    if not check_model_files():
        return
    
    # Check for required packages
    missing_packages = []
    required_packages = ['streamlit', 'pandas', 'numpy', 'scikit-learn', 'xgboost', 'joblib', 'plotly']
    
    for package in required_packages:
        if not check_package(package):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 Missing packages: {', '.join(missing_packages)}")
        install_choice = input("Install missing packages? (y/n): ").lower().strip()
        if install_choice == 'y':
            if not install_requirements():
                return
        else:
            print("❌ Cannot run without required packages.")
            return
    
    # Choose application type
    print("\n🌐 Choose application type:")
    print("1. Streamlit (Recommended - Interactive web interface)")
    print("2. Flask (Simple web interface)")
    print("3. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == '1':
            run_streamlit()
            break
        elif choice == '2':
            run_flask()
            break
        elif choice == '3':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")

if __name__ == "__main__":
    main()
